
import torch

def quantize(x, bits):
    """
    量化音频信号 (PyTorch 实现)
    
    参数：
    x: 输入张量，假设其范围在 [-1, 1] 之间
    bits: 量化的位数（如 8 表示 8 位量化）
    
    返回：
    量化后的整数张量
    """
    # 限制 x 的范围在 [-1, 1] 之间
    x = torch.clamp(x, -1.0, 1.0)
    
    # 将信号映射到 [0, 2^bits - 1] 的整数范围
    quant = ((x + 1.0) / 2.0) * (2 ** bits - 1)
    
    # 将结果转换为整数类型
    return quant.long()


def dequantize(quant, bits):
    """
    将量化后的整数还原为原始的 [-1, 1] 范围内的浮点数

    参数：
    quant: 输入张量，包含量化后的整数值
    bits: 量化的位数（如 8 表示 8 位量化）
    
    返回：
    还原后的浮点张量
    """
    # 计算出量化的最大值
    max_value = 2 ** bits - 1
    
    # 将整数范围 [0, max_value] 映射回 [0, 1]
    x = quant.float() / max_value
    
    # 将 [0, 1] 转换回 [-1, 1]
    return x * 2.0 - 1.0