import torch
from torch import nn
import torch.nn.functional as F
from models.modules.sLSTM import SequentialModule
from models.modules.ResNet2D import SequentialResNet
from utils.quant import quantize, dequantize

class Model(nn.Module) :
    def __init__(self, resnet_layer_configs, hidden_size, track_dims, 
                 bits, num_layers=2, use_fc=False, rnn_type='LSTM', training=True):
        super().__init__()
        self.track_dims = track_dims
        self.bits = bits
        self.n_classes = 2**bits
        self.training = training
        self.hidden_size = hidden_size
        if use_fc:
            self.aux_dims = 1024
        else:
            self.aux_dims = 32//4
            
        self.seq_resnet = SequentialResNet(resnet_layer_configs, use_fc=use_fc)
        
        self.aux_idx = [self.aux_dims * i for i in range(5)]
        
        self.rnn1 = SequentialModule(input_size=track_dims, 
                                            hidden_size=hidden_size, 
                                            num_layers=num_layers, 
                                            rnn_type=rnn_type)
    
        self.fc1 = nn.Linear(hidden_size, hidden_size)
        self.fc2 = nn.Linear(hidden_size, hidden_size)
        self.fc3 = nn.Linear(self.aux_dims*4, hidden_size)

        self.dense1 = nn.Linear(hidden_size // 2, self.n_classes)
        self.dense2 = nn.Linear(hidden_size // 2, self.n_classes)

            
    def forward(self, track, feat):
        # feat 形状 (B, L, C=1, H, W)
        # track 形状 (B, L, track_dims=2)
        batch_size = feat.size(0)
        resnet_output = self.seq_resnet(feat)  # (B, L, C') 或 (B, L, N)
        # track[:,0,:] = torch.zeros(batch_size, self.track_dims).to(feat.device)
        x, _  = self.rnn1(track[:,:-1,:]) # (B, L-1, hidden_size)
        x = self.fc1(x)
        x = F.dropout(x, p=0.5, training=self.training)
        predict = torch.relu(self.fc2(x)) # (B, L-1, class)
        predict = F.dropout(predict, p=0.5, training=self.training)
        measure = torch.relu(self.fc3(resnet_output[:,1:,:])) # (B, L-1, class)
        random_number = torch.randint(0, 3, (1,)).item()  # 生成随机数并转换为 Python 数字 
        random_number = 2
        if random_number == 0: 
            outputs = (predict + measure)/2 # (B, L-1, class)
        elif random_number == 1:
            outputs = predict
        else:    
            outputs = measure
        
        part1, part2 = torch.split(outputs, self.hidden_size // 2, dim=-1)
        part1 = self.dense1(part1)  # 形状 (B, L-1, class)
        part1 = F.dropout(part1, p=0.5, training=self.training)
        part2 = self.dense2(part2)  # 形状 (B, L-1, class)
        part2 = F.dropout(part2, p=0.5, training=self.training)
        combined_output = torch.stack([part1, part2], dim=2)  # 形状 (B, L-1, track_dims=2, class)

        return F.log_softmax(combined_output, dim=-1) # track 形状 (B, L, track_dims=2, class)
        


    def inference(self, track, feat:torch.Tensor):
        """
        推理函数
        :param feat: 输入特征 (B, L, C=1, H, W)
        :return: 模型的推理输出 (B, L, track_dims=2, class)
        """
        # 初始化一个空的 track 预测列表
        batch_size = feat.size(0)
        seq_len = feat.size(1) #L-1
        predicted_track = torch.zeros(batch_size, seq_len, self.track_dims).to(feat.device)
        
        # 初始的 track 通常是可以用先验或者初始状态给出的
        # 例如，使用第一个时间步的 resnet 特征的输出作为初始 track
        initial_track = torch.zeros(batch_size, 1, self.track_dims).to(feat.device)
        predicted_track[:, 0, :] = initial_track[:, 0, :]
        hidden_state=None
        for t in range(1, seq_len):
            # 对于每个时间步，使用前一步预测的 track
            resnet_output = self.seq_resnet.step(feat[:, t:t+1, :, :, :].squeeze(1))  # 截取前 t+1 的输入 (B, C)
            
            # 使用模型预测 track (当前时间步 t 使用前 t-1 的 track)
            x, hidden_state = self.rnn1(predicted_track[:, t-1:t, :].squeeze(1), hidden_state, step_mode=True)  # (B,  hidden_size)
            x = self.fc1(x)
            predict = torch.relu(self.fc2(x))  # 只使用最后一个时间步的输出 (B, class)

            # 同时获取当前时间步的特征预测
            measure = torch.relu(self.fc3(resnet_output))  # 只获取当前 t 时间步的特征 (B, 1, class)

            # 融合 predict 和 measure
            output = (predict + measure) / 2  # (B, class)
            # 拆分输出
            part1, part2 = torch.split(output, self.n_classes // 2, dim=-1)
            part1 = self.dense1(part1)  # (B, class/2)
            part2 = self.dense2(part2)  # (B, class/2)
            # 拼接成 track 的两个维度
            combined_output = torch.stack([part1, part2], dim=1)  # (B, track_dims=2, class)
            # 记录当前时间步的 track 输出（可以选择使用 softmax 的结果或其他方式）
            predicted_track[:, t, :] = dequantize(F.softmax(combined_output, dim=-1).argmax(dim=-1), bits=self.bits)  # 计算均值作为回归结果

        return predicted_track


def build_model(configs):
    """build model with hparams settings

    """
    # 从配置中获取参数
    model_conf = configs['model_conf']
    track_dims = model_conf['track_dims']
    hidden_size = model_conf['hidden_size']
    bits = model_conf['bits']
    num_layers = model_conf['num_layers']
    use_fc = model_conf['use_fc']
    rnn_type = model_conf['rnn_type']
    resnet_layer_configs = model_conf['resnet_layer_configs']
    training = model_conf['training']
    # 初始化模型
    model = Model(resnet_layer_configs=resnet_layer_configs, 
                hidden_size=hidden_size, 
                track_dims=track_dims, 
                bits=bits, 
                num_layers=num_layers, 
                use_fc=use_fc, 
                rnn_type=rnn_type,
                training = training)

    return model 
