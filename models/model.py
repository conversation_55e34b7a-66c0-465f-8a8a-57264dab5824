import torch
from torch import nn
import torch.nn.functional as F
from models.modules.sLSTM import SequentialModule
from models.modules.ResNet2D import SequentialResNet

class Model(nn.Module) :
    def __init__(self, resnet_layer_configs, hidden_size, track_dims, 
                 bits, num_layers=1, use_fc=False, use_gru=False):
        super().__init__()
        self.n_classes = 2**bits
        if use_fc:
            self.aux_dims = 1024
        else:
            self.aux_dims = 32//4
            
        self.seq_resnet = SequentialResNet(resnet_layer_configs, use_fc=use_fc)
        
        self.aux_idx = [self.aux_dims * i for i in range(5)]
        
        self.rnn1 = SequentialModule(input_size=hidden_size, 
                                            hidden_size=hidden_size, 
                                            num_layers=num_layers, 
                                            use_gru=use_gru)
        
        self.rnn2 = SequentialModule(input_size=hidden_size+self.aux_dims, 
                                    hidden_size=hidden_size, 
                                    num_layers=num_layers, 
                                    use_gru=use_gru)

        self.fc1 = nn.Linear(track_dims+self.aux_dims, hidden_size)
        self.fc2 = nn.Linear(hidden_size+self.aux_dims, hidden_size)
        self.fc3 = nn.Linear(hidden_size+self.aux_dims, hidden_size)
        self.fc4 = nn.Linear(hidden_size, self.n_classes)
        self.fc5 = nn.Linear(self.aux_dims*4, self.n_classes)

        self.dense1 = nn.Linear(self.n_classes // 2, self.n_classes)
        self.dense2 = nn.Linear(self.n_classes // 2, self.n_classes)

    
    def forward(self, track, feat):
        # feat 形状 (B, L, C=1, H, W)
        # track 形状 (B, L, track_dims=2)
        resnet_output = self.seq_resnet(feat)  # (B, L, C') 或 (B, L, N)
        resnet_output_chunk_0 = resnet_output[:, :, self.aux_idx[0]:self.aux_idx[1]]
        resnet_output_chunk_1 = resnet_output[:, :, self.aux_idx[1]:self.aux_idx[2]]
        resnet_output_chunk_2 = resnet_output[:, :, self.aux_idx[2]:self.aux_idx[3]]
        resnet_output_chunk_3 = resnet_output[:, :, self.aux_idx[3]:self.aux_idx[4]]
        
        x = torch.cat([track[:,:-1,:], resnet_output_chunk_0[:,:-1,:]], dim=-1) # (B, L-1, C'+track_dims) 或 (B, L-1, N+track_dims)
        x = self.fc1(x)
        res = x 
        x, _  = self.rnn1(x) # (B, L, hidden_size)
        x = x + res
        res = x
        x = torch.cat([x, resnet_output_chunk_1[:,:-1,:]], dim=-1) # (B, L-1, hidden_size+aux_dims//4)
        x, _  = self.rnn2(x) # (B, L, hidden_size)
        x = x + res
        res = x 
        x = torch.cat([x, resnet_output_chunk_2[:,:-1,:]], dim=-1) # (B, L-1, hidden_size+aux_dims//4)
        x = self.fc2(x)
        x = x + res
        x = torch.cat([x, resnet_output_chunk_3[:,:-1,:]], dim=-1) # (B, L-1, hidden_size+aux_dims//4)
        x = self.fc3(x)
        predict = self.fc4(x) # (B, L-1, class)
        measure = self.fc5(resnet_output[:,1:,:]) # (B, L-1, class)
        outputs = (predict + measure)/2 # (B, L-1, class)
        
        part1, part2 = torch.split(outputs, self.n_classes // 2, dim=-1)
        part1 = self.dense1(part1)  # 形状 (B, L-1, class)
        part2 = self.dense2(part2)  # 形状 (B, L-1, class)
        combined_output = torch.stack([part1, part2], dim=2)  # 形状 (B, L-1, track_dims=2, class)

        return F.log_softmax(combined_output, dim=-1) # track 形状 (B, L, track_dims=2, class)
        


    def generate(self, spectrum):

        self.eval()
        outputs = []
        with torch.no_grad():
            b_size, seq_len, _ = spectrum.size()


        self.train()
        return outputs





def build_model(configs):
    """build model with hparams settings

    """
    # 从配置中获取参数
    model_conf = configs['model_conf']
    track_dims = model_conf['track_dims']
    hidden_size = model_conf['hidden_size']
    bits = model_conf['bits']
    num_layers = model_conf['num_layers']
    use_fc = model_conf['use_fc']
    use_gru = model_conf['use_gru']
    resnet_layer_configs = model_conf['resnet_layer_configs']
    # 初始化模型
    model = Model(resnet_layer_configs=resnet_layer_configs, 
                hidden_size=hidden_size, 
                track_dims=track_dims, 
                bits=bits, 
                num_layers=num_layers, 
                use_fc=use_fc, 
                use_gru=use_gru)

    return model 
