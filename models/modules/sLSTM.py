import torch
from torch import nn
import torch.nn.functional as F

class sLSTMCell(nn.Module):
    """
    sLSTM cell implementation.

    This cell uses exponential gating as described in the xLSTM paper.

    Args:
        input_size (int): Size of input features.
        hidden_size (int): Size of hidden state.
    """

    def __init__(self, input_size, hidden_size):
        super(sLSTMCell, self).__init__()
        self.input_size = input_size
        self.hidden_size = hidden_size
        
        self.weight_ih = nn.Parameter(torch.randn(4 * hidden_size, input_size))
        self.weight_hh = nn.Parameter(torch.randn(4 * hidden_size, hidden_size))
        self.bias = nn.Parameter(torch.randn(4 * hidden_size))
        
        self.reset_parameters()

    def reset_parameters(self):
        """Initialize parameters using Xavier uniform initialization."""
        nn.init.xavier_uniform_(self.weight_ih)
        nn.init.xavier_uniform_(self.weight_hh)
        nn.init.zeros_(self.bias)

    def forward(self, input, hx):
        """
        Forward pass of the sLSTM cell.

        Args:
            input (Tensor): Input tensor of shape (batch_size, input_size).
            hx (tuple of Tensors): Previous hidden state and cell state.

        Returns:
            tuple: New hidden state and cell state.
        """
        h, c, n = hx
        gates = F.linear(input, self.weight_ih, self.bias) + F.linear(h, self.weight_hh)
        
        i, f, g, o = gates.chunk(4, 1)
        
        i = torch.exp(i)  # Exponential input gate
        f = torch.sigmoid(f)  # Exponential forget gate
        g = torch.tanh(g)
        o = torch.sigmoid(o)
        n = f * n + i
        c = f * c + i * g
        h = o * (c / n)
        
        return h, c, n


class sLSTM(nn.Module):
    """
    sLSTM layer implementation.

    This layer applies multiple sLSTM cells in sequence, with optional dropout between layers.

    Args:
        input_size (int): Size of input features.
        hidden_size (int): Size of hidden state.
        num_layers (int): Number of sLSTM layers.
        dropout (float, optional): Dropout probability between layers. Default: 0.0.
    """

    def __init__(self, input_size, hidden_size, num_layers, dropout=0.0):
        super(sLSTM, self).__init__()
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.dropout = dropout

        # self.layers = nn.ModuleList([sLSTMCell(input_size if i == 0 else hidden_size, hidden_size) 
        #                              for i in range(num_layers)])
        self.layers2 = nn.ModuleList([nn.LSTM(input_size if i == 0 else hidden_size, hidden_size, 1, batch_first=True) 
                                     for i in range(num_layers)])
        self.dropout_layer = nn.Dropout(dropout)

    def step(self, input_seq, hidden_state=None):
        batch_size, seq_length, _ = input_seq.size()
        
        if hidden_state is None:
            hidden_state = self.init_hidden(batch_size)

        x = input_seq.squeeze(1)
        for layer_idx, layer in enumerate(self.layers):
            h, c, n = hidden_state[layer_idx]
            h, c, n = layer(x, (h, c, n))
            hidden_state[layer_idx] = (h, c, n)
            x = self.dropout_layer(h) if layer_idx < self.num_layers - 1 else h
        return x.unsqueeze(1), hidden_state

    def step_lstm(self, input_seq, hidden_state=None):
        batch_size, seq_length, _ = input_seq.size()
        
        if hidden_state is None:
            hidden_state = [(torch.zeros(1, batch_size, self.hidden_size, device = next(self.layers2[0].parameters()).device),
                 torch.zeros(1, batch_size, self.hidden_size, device = next(self.layers2[0].parameters()).device))
                for _ in range(self.num_layers)]

        x = input_seq
        for layer_idx, layer in enumerate(self.layers2):
            
            h, c = hidden_state[layer_idx]
            y, (h, c) = layer(x, (h, c))
            hidden_state[layer_idx] = (h, c)
            x = self.dropout_layer(y) if layer_idx < self.num_layers - 1 else y
        return x, hidden_state
    
        

    def forward(self, input_seq, hidden_state=None):
        """
        Forward pass of the sLSTM layer.

        Args:
            input_seq (Tensor): Input sequence of shape (batch_size, seq_length, input_size).
            hidden_state (tuple of Tensors, optional): Initial hidden state. Default: None.

        Returns:
            tuple: Output sequence and final hidden state.
        """
        batch_size, seq_length, _ = input_seq.size()
        
        if hidden_state is None:
            hidden_state = self.init_hidden(batch_size)
        
        outputs = []
        for t in range(seq_length):
            x = input_seq[:, t, :]
            for layer_idx, layer in enumerate(self.layers):
                h, c, n = hidden_state[layer_idx]
                h, c, n = layer(x, (h, c, n))
                hidden_state[layer_idx] = (h, c, n)
                x = self.dropout_layer(h) if layer_idx < self.num_layers - 1 else h
            outputs.append(x)
        
        return torch.stack(outputs, dim=1), hidden_state

    def init_hidden(self, batch_size):
        """Initialize hidden state for all layers."""
        return [(torch.zeros(batch_size, self.hidden_size, device=self.layers[0].device),
                 torch.zeros(batch_size, self.hidden_size, device=self.layers[0].device),
                 torch.ones(batch_size, self.hidden_size, device=self.layers[0].device))
                #  torch.ones(batch_size, self.hidden_size, device=self.layers[0].weight_ih.device))
                for _ in range(self.num_layers)]


class SequentialModule(nn.Module):
    def __init__(self, input_size, hidden_size, num_layers=1, rnn_type='LSTM'):
        super(SequentialModule, self).__init__()
        self.rnn_type = rnn_type
        self.hidden_size = hidden_size
        if self.rnn_type == 'GRU':
            self.rnn = nn.GRU(input_size=input_size, 
                              hidden_size=hidden_size, 
                              num_layers=num_layers, 
                              batch_first=True)
        else:
            self.rnn = nn.LSTM(input_size=input_size, 
                               hidden_size=hidden_size, 
                               num_layers=num_layers, 
                               batch_first=True)

    def forward(self, x, hidden_state=None, step_mode=False):
        """
        x: 输入的张量，形状为 (B, L, input_size) 或 (B, input_size)
        hidden_state: 隐藏状态 (h_n, c_n) 对应于 LSTM 或 (h_n) 对应于 GRU
        step_mode: 如果为 True，表示单步操作，输入形状为 (B, input_size)
        """
        if step_mode:
            # 单步操作，输入形状为 (B, input_size)
            x = x.unsqueeze(1)  # 增加时间步维度，(B, 1, input_size)

        # RNN 前向传播
        output, hidden_state = self.rnn(x, hidden_state)

        if step_mode:
            output = output.squeeze(1)  # 移除时间步维度，返回形状为 (B, hidden_size)
        
        return output, hidden_state


    
class SequentialModuleCell(nn.Module):
    def __init__(self, input_size, hidden_size, use_gru=False):
        super(SequentialModuleCell, self).__init__()
        self.use_gru = use_gru
        self.hidden_size = hidden_size

        if self.use_gru:
            self.rnn_cell = nn.GRUCell(input_size=input_size, hidden_size=hidden_size)
        else:
            self.rnn_cell = nn.LSTMCell(input_size=input_size, hidden_size=hidden_size)

    def forward(self, x, hidden_state):
        """
        x: 单个时间步的输入，形状为 (B, input_size)
        hidden_state: 对于 GRU 是 (h_n)，对于 LSTM 是 (h_n, c_n)
        """
        if self.use_gru:
            h_n = self.rnn_cell(x, hidden_state)  # GRU 的隐藏状态更新
            return h_n
        else:
            h_n, c_n = self.rnn_cell(x, hidden_state)  # LSTM 的隐藏状态更新
            return h_n, c_n