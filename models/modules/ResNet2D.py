import torch
from torch import nn
import torch.nn.functional as F

class ResNetBlock(nn.Module):
    def __init__(self, in_channels, out_channels):
        super(ResNetBlock, self).__init__()
        self.padding = 0
        self.conv1 = nn.Conv2d(in_channels, out_channels, kernel_size=3, padding=self.padding)
        self.bn1 = nn.BatchNorm2d(out_channels)
        
        self.conv2 = nn.Conv2d(out_channels, out_channels, kernel_size=3, padding=self.padding)
        self.bn2 = nn.BatchNorm2d(out_channels)

        # 如果输入和输出通道数不同，需要进行调整
        if in_channels != out_channels:
            self.shortcut = nn.Conv2d(in_channels, out_channels, kernel_size=1)
        else:
            self.shortcut = None

    def forward(self, x):
        if self.padding ==0:
            identity = x[:,:,2:-2, 2:-2]
        else:
            identity = x
            
        out = F.relu(self.bn1(self.conv1(x)))
        out = self.bn2(self.conv2(out))

        if self.shortcut is not None:
            identity = self.shortcut(identity)  # 调整输入通道数

        out += identity  # Shortcut connection
        out = F.relu(out)
        
        return out

class ResNet(nn.Module):
    def __init__(self, layer_configs):
        super(ResNet, self).__init__()
        
        self.resnet_blocks = nn.ModuleList()
        for in_channels, out_channels in layer_configs:
            self.resnet_blocks.append(ResNetBlock(in_channels, out_channels))
        
    def forward(self, x):
        for block in self.resnet_blocks:
            x = block(x)
        return x
  
class SequentialResNet(nn.Module):
    def __init__(self, layer_configs, use_fc=False):
        super(SequentialResNet, self).__init__()
        self.resnet_model = ResNet(layer_configs)
        self.use_fc = use_fc
        
    def forward(self, x):
        # 假设输入 x 的形状是 (B, L, C, H, W)
        B, L, C, H, W = x.shape
        output = []
        for t in range(L):
            output.append(self.resnet_model(x[:, t, :, :, :]))  # 处理每个时间步
        output = torch.stack(output, dim=1)  # (B, L, C', H', W')
        if self.use_fc:
            # 假设我们希望展平 H' 和 W'，形成 N
            # 这里的 N = C' * H' * W'
            output = output.view(B, L, -1)  # 展平 H' 和 W' 维度
        else:
            # 应用全局平均池化
            output = F.adaptive_avg_pool2d(output, (1, 1))  # 输出形状 (B, L, C', 1, 1)
            output = output.view(B, L, -1)  # 转换为 (B, L, C')
            
        return output  # 将输出组合成一个张量
    
    # 单步输入
    def step(self, x):
        # 假设输入 x 的形状是 (B, C, H, W)
        B, C, H, W = x.shape
        output = self.resnet_model(x) # (B, C', H', W')

        if self.use_fc:
            # 假设我们希望展平 H' 和 W'，形成 N
            # 这里的 N = C' * H' * W'
            output = output.view(B,  -1)  # 展平 H' 和 W' 维度
        else:
            # 应用全局平均池化
            output = F.adaptive_avg_pool2d(output, (1, 1))  # 输出形状 (B,  C', 1, 1)
            output = output.view(B, -1)  # 转换为 (B,  C')
            
        return output  # 将输出组合成一个张量