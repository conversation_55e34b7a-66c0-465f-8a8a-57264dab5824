# https://blog.csdn.net/Ephemeroptera/article/details/140327980
import torch
import torch.nn as nn

class StreamSelfAttentionEncoder(nn.Module):
    def __init__(self, model_dim, self_attention_size):
        super(StreamSelfAttentionEncoder, self).__init__()
        self.model_dim = model_dim
        self.self_attention_size = self_attention_size
        self.Q = nn.Linear(model_dim, model_dim)
        self.K = nn.Linear(model_dim, model_dim)
        self.V = nn.Linear(model_dim, model_dim)
        self.softmax = nn.Softmax(dim=-1)
        
        # FFN
        self.ffn = nn.Sequential(
            nn.Linear(model_dim, model_dim * 4),
            nn.ReLU(),
            nn.Linear(model_dim * 4, model_dim)
        )

    def forward(self, x, k_cache=None, v_cache=None, pos=None):
        # Ensure positional encoding is on the same device as x
        if pos is not None:
            pos_enc = self.get_positional_encoding(pos, self.model_dim, x.device)  # (model_dim,)
            x = x + pos_enc.unsqueeze(0).unsqueeze(1)  # (N, 1, model_dim)
        
        # Project inputs to Q, K, V
        q = self.Q(x)  # (N, 1, model_dim)
        k = self.K(x)  # (N, 1, model_dim)
        v = self.V(x)  # (N, 1, model_dim)
        
        batch_size = x.size(0)
        
        # Initialize k_cache and v_cache if not provided
        if k_cache is None:
            k_cache = torch.zeros((batch_size, 0, self.model_dim), device=x.device)
            v_cache = torch.zeros((batch_size, 0, self.model_dim), device=x.device)
        
        # Concatenate past K, V with current K, V
        k_cache = torch.cat([k_cache, k], dim=1)  # (N, seq_len + 1, model_dim)
        v_cache = torch.cat([v_cache, v], dim=1)  # (N, seq_len + 1, model_dim)
        
        # Compute attention scores
        attn_scores = torch.matmul(q, k_cache[:, -self.self_attention_size:].transpose(-2, -1)) / torch.sqrt(self.model_dim)  # (N, 1, attention_size)
        attn_weights = self.softmax(attn_scores)  # (N, 1, attention_size)
        
        # Compute attention output
        attn_output = torch.matmul(attn_weights, v_cache[:, -self.self_attention_size:])  # (N, 1, model_dim)
        
        # Apply skip connection and FFN
        attn_output = attn_output + x  # (N, 1, model_dim)
        ffn_output = self.ffn(attn_output)  # (N, 1, model_dim)
        output = ffn_output + attn_output  # (N, 1, model_dim)
        
        return output, k_cache, v_cache

    def get_positional_encoding(self, pos, model_dim, device):
        pe = torch.zeros(model_dim, device=device)
        div_term = torch.exp(torch.arange(0, model_dim, 2, device=device).float() * (-torch.log(10000.0) / model_dim))
        pe[0::2] = torch.sin(pos * div_term)
        pe[1::2] = torch.cos(pos * div_term)
        return pe  # (model_dim,)

class StreamSelfAttentionDecoder(nn.Module):
    def __init__(self, model_dim, self_attention_size, cross_attention_size):
        super(StreamSelfAttentionDecoder, self).__init__()
        self.model_dim = model_dim
        self.self_attention_size = self_attention_size
        self.cross_attention_size = cross_attention_size
        self.Qe = nn.Linear(model_dim, model_dim)
        self.Qd = nn.Linear(model_dim, model_dim)
        self.Kd = nn.Linear(model_dim, model_dim)
        self.Vd = nn.Linear(model_dim, model_dim)
        self.softmax = nn.Softmax(dim=-1)
        
        # FFN
        self.ffn = nn.Sequential(
            nn.Linear(model_dim, model_dim * 4),
            nn.ReLU(),
            nn.Linear(model_dim * 4, model_dim)
        )

    def forward(self, x,
                encoder_k_cache,
                encoder_v_cache,
                decoder_k_cache=None,
                decoder_v_cache=None, 
                pos=None):
        
        batch_size = x.size(0)

        # Ensure positional encoding is on the same device as x
        if pos is not None:
            pos_enc = self.get_positional_encoding(pos, self.model_dim, x.device)
            x = x + pos_enc.unsqueeze(0).unsqueeze(1)  # (N, 1, model_dim)
        
        # Initialize caches if not provided
        if decoder_k_cache is None:
            decoder_k_cache = torch.zeros((batch_size, 0, self.model_dim), device=x.device)
            decoder_v_cache = torch.zeros((batch_size, 0, self.model_dim), device=x.device)
        
        # Decoder self-attention
        qd = self.Qd(x)  # (N, 1, model_dim)
        kd = self.Kd(x)  # (N, 1, model_dim
        vd = self.Vd(x)  # (N, 1, model_dim)

        # Concatenate past K, V with current K, V
        decoder_k_cache = torch.cat([decoder_k_cache, kd], dim=1)  # (N, seq_len + 1, model_dim)
        decoder_v_cache = torch.cat([decoder_v_cache, vd], dim=1)  # (N, seq_len + 1, model_dim)
        
        # Compute self-attention scores
        attn_self_scores = torch.matmul(qd, decoder_k_cache[:, -self.self_attention_size:].transpose(-2, -1)) / torch.sqrt(self.model_dim)
        attn_self_weights = self.softmax(attn_self_scores)
        attn_self_output = torch.matmul(attn_self_weights, decoder_v_cache[:, -self.self_attention_size:])
        attn_self_output = attn_self_output + x

        # Encoder-decoder cross-attention
        qe = self.Qe(attn_self_output)
        attn_cross_scores = torch.matmul(qe, encoder_k_cache[:, -self.cross_attention_size:].transpose(-2, -1)) / torch.sqrt(self.model_dim)
        attn_cross_weights = self.softmax(attn_cross_scores)
        attn_cross_output = torch.matmul(attn_cross_weights, encoder_v_cache[:, -self.cross_attention_size:])
        attn_cross_output = attn_cross_output + attn_self_output

        # Apply skip connection and FFN
        ffn_output = self.ffn(attn_cross_output)
        output = ffn_output + attn_cross_output
        
        return output, decoder_k_cache, decoder_v_cache

    def get_positional_encoding(self, pos, model_dim, device):
        pe = torch.zeros(model_dim, device=device)
        div_term = torch.exp(torch.arange(0, model_dim, 2, device=device).float() * (-torch.log(10000.0) / model_dim))
        pe[0::2] = torch.sin(pos * div_term)
        pe[1::2] = torch.cos(pos * div_term)
        return pe


if __name__ == "__main__":
    batch_size = 2
    model_dim = 64
    attention_size = 10
    self_attention_size = 8
    cross_attention_size = 6
    seq_len = 1
    decoder_step = 2
    device = 'cuda' if torch.cuda.is_available() else 'cpu'

    # 实例化自注意力编码器和解码器
    encoder = StreamSelfAttentionEncoder(model_dim, attention_size).to(device)
    decoder = StreamSelfAttentionDecoder(model_dim, self_attention_size, cross_attention_size).to(device)
    
    encoder_k_cache = encoder_v_cache = None
    decoder_k_cache = decoder_v_cache = None
    
    encoder_outputs = torch.zeros(batch_size, 0, model_dim).to(device)
    decoder_outputs = torch.zeros(batch_size, 1, model_dim).to(device)
    
    for t in range(10):
        x = torch.rand(batch_size, seq_len, model_dim).to(device)  # 模拟时间步t的输入
        pos = t  # 当前的位置
        
        # 编码器前向传递
        encoder_output, encoder_k_cache, encoder_v_cache = encoder(x, encoder_k_cache, encoder_v_cache, pos)
        encoder_outputs = torch.cat([encoder_outputs, encoder_output], dim=1)
        print("##")
        print(f"Encoder Output shape at time step {t}: {encoder_output.shape}")  # (N, 1, model_dim)
        print(f"Encoder k_cache shape: {encoder_k_cache.shape}")  # (N, seq_len + 1, model_dim)
        print(f"Encoder v_cache shape: {encoder_v_cache.shape}")  # (N, seq_len + 1, model_dim)
        print()

        if t % decoder_step == 0:
            # 解码器前向传递
            decoder_output, decoder_k_cache, decoder_v_cache = decoder(decoder_outputs[:, -1:], encoder_outputs, encoder_outputs, decoder_k_cache, decoder_v_cache, pos)
            decoder_outputs = torch.cat([decoder_outputs, decoder_output], dim=1)
            print("@@")
            print(f"Decoder Output shape at time step {t}: {decoder_output.shape}")  # (N, 1, model_dim)
            print(f"Decoder k_cache shape: {decoder_k_cache.shape}")  # (N, seq_len + 1, model_dim)
            print(f"Decoder v_cache shape: {decoder_v_cache.shape}")  # (N, seq_len + 1, model_dim)
            print()
