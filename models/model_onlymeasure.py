import torch
from torch import nn
import torch.nn.functional as F
from models.modules.sLSTM import SequentialModule
from models.modules.ResNet2D import SequentialResNet
from utils.quant import quantize, dequantize
import torchvision.models as models


# Step 2: 定义简单的CNN模型
class SpotDetector(nn.Module):
    def __init__(self):
        super(SpotDetector, self).__init__()
        self.conv1 = nn.Conv2d(1, 16, kernel_size=3, stride=1, padding=1)
        self.conv2 = nn.Conv2d(16, 32, kernel_size=3, stride=1, padding=1)
        self.conv3 = nn.Conv2d(32, 4, kernel_size=3, stride=1, padding=1)
        self.fc1 = nn.Linear(4*64*64, 128)
        self.fc2 = nn.Linear(128, 2)  # 输出2个值 (x, y)

    def forward(self, x):
        x = torch.relu(self.conv1(x))
        x = torch.relu(self.conv2(x))
        x = torch.relu(self.conv3(x))
        # x = F.adaptive_avg_pool2d(x, (1, 1))
        # x = x.squeeze()    
        x = torch.flatten(x, 1)
        x = torch.relu(self.fc1(x))
        x = self.fc2(x)
        return x  # 返回中心坐标 (x, y)
  
  
# 定义自定义模型，将 ResNet 作为主干网络
class CustomModel(nn.Module):
    def __init__(self, backbone='res18', num_classes=10, pretrained=True):
        super(CustomModel, self).__init__()
        
        self.spotdetector = SpotDetector()
    
    def forward(self, track, x):
        # 使用主干网络提取特征
        # x = x.repeat(1, 1, 3, 1, 1)  # 复制通道，保持 batch_size, height, width 不变

        B, L, C, H, W = x.shape
        outputs = []
        for t in range(L):
            outputs.append(self.spotdetector(x[:, t, :, :, :]))  # 处理每个时间步
            
        outputs = torch.stack(outputs, dim=1)  # (B, L, C', H', W')
        
        return outputs
    

        
class Model(nn.Module) :
    def __init__(self, resnet_layer_configs, hidden_size, track_dims, 
                 bits, num_layers=2, use_fc=False, rnn_type='LSTM', training=True):
        super().__init__()
        self.track_dims = track_dims
        self.bits = bits
        self.n_classes = track_dims
        self.training = training
        self.hidden_size = hidden_size
        if use_fc:
            self.aux_dims = 260
        else:
            self.aux_dims = 32//4
            
        self.seq_resnet = SequentialResNet(resnet_layer_configs, use_fc=use_fc)
        
        self.fc3 = nn.Linear(2704, hidden_size)

        self.dense1 = nn.Linear(hidden_size, self.n_classes)

            
    def forward(self, track, feat):

        resnet_output = self.seq_resnet(feat)  # (B, L, C') 或 (B, L, N)
        measure = torch.relu(self.fc3(resnet_output[:,:,:])) # (B, L-1, class)
        output = self.dense1(measure)  # 形状 (B, L-1, 2*class)
  
        return output
        


    def inference(self, track, feat:torch.Tensor):
        """
        推理函数
        :param feat: 输入特征 (B, L, C=1, H, W)
        :return: 模型的推理输出 (B, L, track_dims=2, class)
        """
        # 初始化一个空的 track 预测列表
        batch_size = feat.size(0)
        seq_len = feat.size(1) #L-1
        predicted_track = torch.zeros(batch_size, seq_len, self.track_dims).to(feat.device)
        
        # 初始的 track 通常是可以用先验或者初始状态给出的
        # 例如，使用第一个时间步的 resnet 特征的输出作为初始 track
        initial_track = torch.zeros(batch_size, 1, self.track_dims).to(feat.device)
        predicted_track[:, 0, :] = initial_track[:, 0, :]
        hidden_state=None
        for t in range(1, seq_len):
            # 对于每个时间步，使用前一步预测的 track
            resnet_output = self.seq_resnet.step(feat[:, t:t+1, :, :, :].squeeze(1))  # 截取前 t+1 的输入 (B, C)
            
            # 使用模型预测 track (当前时间步 t 使用前 t-1 的 track)
            x, hidden_state = self.rnn1(predicted_track[:, t-1:t, :].squeeze(1), hidden_state, step_mode=True)  # (B,  hidden_size)
            x = self.fc1(x)
            predict = torch.relu(self.fc2(x))  # 只使用最后一个时间步的输出 (B, class)

            # 同时获取当前时间步的特征预测
            measure = torch.relu(self.fc3(resnet_output))  # 只获取当前 t 时间步的特征 (B, 1, class)

            # 融合 predict 和 measure
            output = (predict + measure) / 2  # (B, class)
            # 拆分输出
            part1, part2 = torch.split(output, self.n_classes // 2, dim=-1)
            part1 = self.dense1(part1)  # (B, class/2)
            part2 = self.dense2(part2)  # (B, class/2)
            # 拼接成 track 的两个维度
            combined_output = torch.stack([part1, part2], dim=1)  # (B, track_dims=2, class)
            # 记录当前时间步的 track 输出（可以选择使用 softmax 的结果或其他方式）
            predicted_track[:, t, :] = dequantize(F.softmax(combined_output, dim=-1).argmax(dim=-1), bits=self.bits)  # 计算均值作为回归结果

        return predicted_track

class ModelRNN(Model):
    def __init__(self, *args, **kwargs):
        super(ModelRNN, self).__init__(*args, **kwargs)
        self.num_layers = kwargs.get('num_layers', 2)  # 从 kwargs 中获取 rnn_type，默认 LSTM
        self.rnn_type = kwargs.get('rnn_type', 'LSTM')  # 从 kwargs 中获取 rnn_type，默认 LSTM  

        self.rnn1 = SequentialModule(input_size=self.hidden_size, 
                                    hidden_size=self.hidden_size, 
                                    num_layers=self.num_layers, 
                                    rnn_type=self.rnn_type)

    def forward(self, track, feat):

        resnet_output = self.seq_resnet(feat)  # (B, L, C') 或 (B, L, N)
        measure = torch.relu(self.fc3(resnet_output[:,:,:])) # (B, L-1, class)
        output, _  = self.rnn1(measure)
        output = self.dense1(output)  # 形状 (B, L-1, 2*class)
        
  
        return output
 
 
class ModelKF(Model):
    def __init__(self, *args, **kwargs):
        super(ModelKF, self).__init__(*args, **kwargs)
        self.num_layers = kwargs.get('num_layers', 2)  # 从 kwargs 中获取 rnn_type，默认 LSTM
        self.rnn_type = kwargs.get('rnn_type', 'LSTM')  # 从 kwargs 中获取 rnn_type，默认 LSTM  

        self.rnn1 = SequentialModule(input_size=self.hidden_size, 
                                    hidden_size=self.hidden_size, 
                                    num_layers=self.num_layers, 
                                    rnn_type=self.rnn_type)

        self.rnn2 = SequentialModule(input_size=self.n_classes, 
                                    hidden_size=self.n_classes, 
                                    num_layers=self.num_layers, 
                                    rnn_type=self.rnn_type)
        
                # 创建一个可学习的2x2矩阵作为参数
        self.matrix = nn.Parameter(torch.randn(self.n_classes, self.n_classes))
    def forward(self, track, feat):

        resnet_output = self.seq_resnet(feat)  # (B, L, C') 或 (B, L, N)
        measure = torch.relu(self.fc3(resnet_output[:,1:,:])) # (B, L-1, class)
        output, _  = self.rnn1(measure)
        output = self.dense1(output)  # 形状 (B, L-1, 2*class)
        predicted_track, _  = self.rnn2(track[:,:-1,:])
        output = output + torch.matmul((output - predicted_track), self.matrix)
        
        return output
    
    def inference(self, track, feat:torch.Tensor):
        """
        推理函数
        :param feat: 输入特征 (B, L, C=1, H, W)
        :return: 模型的推理输出 (B, L, track_dims=2, class)
        """
        # 初始化一个空的 track 预测列表
        batch_size = feat.size(0)
        seq_len = feat.size(1) #L-1
        predicted_track = torch.zeros(batch_size, seq_len, self.track_dims).to(feat.device)
        
        # 初始的 track 通常是可以用先验或者初始状态给出的
        # 例如，使用第一个时间步的 resnet 特征的输出作为初始 track
        initial_track = torch.zeros(batch_size, 1, self.track_dims).to(feat.device)
        predicted_track[:, 0, :] = initial_track[:, 0, :]
        hidden_state1 = None
        hidden_state2 = None
        for t in range(1, seq_len):
            # 对于每个时间步，使用前一步预测的 track
            resnet_output = self.seq_resnet.step(feat[:, t:t+1, :, :, :].squeeze(1))  # 截取前 t+1 的输入 (B, C)
            measure = torch.relu(self.fc3(resnet_output)) # (B, L-1, class)
            output, hidden_state1  = self.rnn1(measure.unsqueeze(1), hidden_state1)
            output = self.dense1(output)  # 形状 (B, L-1, 2*class)
            # 使用模型预测 track (当前时间步 t 使用前 t-1 的 track)
            x, hidden_state2 = self.rnn2(predicted_track[:, t-1:t, :], hidden_state2)  # (B,  hidden_size)
            output = output + torch.matmul((output - x), self.matrix)
            # 记录当前时间步的 track 输出（可以选择使用 softmax 的结果或其他方式）
            predicted_track[:, t, :] = output

        return predicted_track
           
def build_model(configs):
    """build model with hparams settings

    """
    # 从配置中获取参数
    model_conf = configs['model_conf']
    track_dims = model_conf['track_dims']
    hidden_size = model_conf['hidden_size']
    bits = model_conf['bits']
    num_layers = model_conf['num_layers']
    use_fc = model_conf['use_fc']
    rnn_type = model_conf['rnn_type']
    resnet_layer_configs = model_conf['resnet_layer_configs']
    training = model_conf['training']
    # 初始化模型
    model = ModelKF(resnet_layer_configs=resnet_layer_configs, 
                hidden_size=hidden_size, 
                track_dims=track_dims, 
                bits=bits, 
                num_layers=num_layers, 
                use_fc=use_fc, 
                rnn_type=rnn_type,
                training = training)
    # model = CustomModel(backbone='res18', num_classes=2**bits, pretrained=True)

    return model 
