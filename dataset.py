import os
import random
import numpy as np
import torch
from scipy.signal import get_window
from torch.utils.data import Dataset, DataLoader
seed = 777
np.random.seed(seed)
random.seed(seed)

preprocess = False

new_parent_folder = '/media/kb501/diskA/CQ/Tkwer/simmimo24'
def generate_steering(tx_num, rx_num, k, lamada, pisearchx, device):
    """
    生成导向矢量 (Steering Vector)
    tx_num: 发射天线数量
    rx_num: 接收天线数量
    k: 波数
    lamada: 波长
    pisearchx: 搜索角度
    """
    # 创建一个空的列表来存储导向矢量
    a_phi = []

    # Generate steering vector for each angle in pisearchx
    for phi in pisearchx:
        dt = (torch.arange(tx_num * rx_num, dtype=torch.float32, device=device) * lamada / 2) * torch.sin(torch.tensor(np.deg2rad(-phi), device='cuda'))
        a = torch.exp(-1j * k * dt)
        a_phi.append(a)

    return torch.stack(a_phi, dim=1)  # 使用 dim=1 在第二维堆叠

k = 2*np.pi*(60e9)/3e8
lamada1 = 3e8/60e9
pisearchx = np.linspace(-90, 90, 64)  # Example
a_phi = generate_steering(tx_num=2, rx_num=4, k=k, lamada=lamada1, pisearchx=pisearchx, device='cuda')


def read_and_process_txt(txt_file_path):
    # 读取轨迹长度
    with open(txt_file_path, 'r') as file:
        trajectory_length = int(file.readline().strip())
        
        # 读取剩余的坐标数据
        x = []
        y = []
        for line in file:
            x_i, y_i = line.strip().split()
            x.append(float(x_i))
            y.append(float(y_i))
        x = np.array(x) 
        y = np.array(y) 
        # 计算 x 的均值
        mean_x = np.mean(x)
        
        # 归一化
        normalized_x = (x - mean_x) / 2000
        # normalized_y = 0.40 - y / 2000
        normalized_y =  y / 2000
        
        # 组合归一化后的结果
        normalized_data = np.vstack((normalized_x, normalized_y)).T
        coordinates_tensor = torch.tensor(normalized_data, dtype=torch.float32)
    return trajectory_length, coordinates_tensor.cuda()


def mvdr_beamforming(file_path, fft1d_num=32, perSymb_SNR_dB=0, simu_data=False, downsample=2, pisearchx=None, a_phi=None):
    # 读取 .npy 文件并转换为 PyTorch 张量
    rawData = np.load(file_path).astype(np.complex64)
    rawData = torch.tensor(rawData, dtype=torch.complex64).cuda()  # 转换为复数类型并移动到 GPU
    
    # 添加噪声
    if simu_data:
        noise_power = torch.tensor(10 ** (-perSymb_SNR_dB / 10))
        noise = torch.sqrt(noise_power / 2) * (torch.randn(rawData.shape).cuda() + 1j * torch.randn(rawData.shape).cuda())
        rawData += noise
    
    frame_num = rawData.shape[0]
    # Permute and downsample
    adc_data = rawData.permute(0, 1, 3, 2)  # PyTorch 的 permute
    adc_data = downsample * adc_data[:frame_num, :, :, ::downsample]

    # 均值相消
    adc_data = adc_data - adc_data.mean(1, keepdim=True)
    # Generate window (Hamming window)
    shape = adc_data.shape
    w = torch.tensor(get_window('hamming', shape[3]), dtype=torch.float32).cuda().view(1, 1, 1, -1)  # 移动到 GPU
    
    # 窗口加权
    data = adc_data * w
    
    # FFT 计算
    radar_cube = torch.fft.fft(data, n=fft1d_num, dim=3) #frame doppler antenna range
    relatematrix = torch.matmul(radar_cube.conj().permute(0, 3, 2, 1), radar_cube.permute(0, 3, 1, 2))  # 计算所有相关矩阵
    # 初始化输出
    range_azimuth = torch.zeros((frame_num, fft1d_num, len(pisearchx)), dtype=torch.float32).cuda()


    # 计算 MVDR
    for phi in range(len(pisearchx)):
        a = a_phi[:, phi].to(torch.complex64).view(-1, 1)
        data = torch.matmul(relatematrix, a.conj())
        MVDR = torch.abs(torch.matmul(a.T, data))
        range_azimuth[:, :, phi] = MVDR.squeeze()  # 存储结果

    # 遍历 Batch 中的每一个图像
    min_val = range_azimuth.view(range_azimuth.shape[0], -1).min(dim=1)[0].view(-1, 1, 1)
    max_val = range_azimuth.view(range_azimuth.shape[0], -1).max(dim=1)[0].view(-1, 1, 1)
    
    # 防止除以零，避免 max_val 和 min_val 相等时产生 NaN
    normalized_tensor = (range_azimuth - min_val) / (max_val - min_val + 1e-8)
    save_preprocess = False
    if save_preprocess:
        # 获取原文件的父级目录
        old_parent_folder = file_path.split(os.path.sep)[0]  # 获取旧的顶层父级文件夹
        relative_path = file_path[len(old_parent_folder)+1:]  # 获取子目录结构及文件名
        relative_path = os.path.splitext(relative_path)[0] + '.pt'
        # 构建新的完整路径，替换顶层父级文件夹
        new_file_path = os.path.join(new_parent_folder, relative_path)

        # Step 2: 如果新的父级文件夹路径不存在，则创建
        new_folder_path = os.path.dirname(new_file_path)  # 获取新的文件夹路径
        if not os.path.exists(new_folder_path):
            os.makedirs(new_folder_path)  # 创建新的文件夹
        torch.save(normalized_tensor, new_file_path)
        
    return normalized_tensor

def collate_func(batch):
    global preprocess
    results = [] 
    for file in batch:
        display = False
        # preprocess = False
        trajectory_length, coordinates  = read_and_process_txt(file[0])
        if preprocess:
            result = torch.load(file[1], weights_only=True)
        else:
            result = mvdr_beamforming(file[1], fft1d_num=64, perSymb_SNR_dB=0, simu_data=False,
                                    downsample=2, pisearchx=pisearchx, a_phi=a_phi)
        sample_length = result.shape[0]
        if display:
            import matplotlib.pyplot as plt
            for ra_hotmap in result:
                plt.matshow(ra_hotmap.cpu(), fignum=1)
                plt.pause(0.001)
        # 生成均匀分布的索引，下采样
        indices = np.linspace(0, len(coordinates) - 1, sample_length, dtype=int)
        # 保存结果，包含轨迹长度、坐标和对应的 MVDR 结果
        results.append((sample_length, coordinates[indices], result))

    # 按照 trajectory_length 从小到大排序
    sorted_results = sorted(results, key=lambda x: x[0])

    # 如果需要分开轨迹长度、坐标和结果
    sorted_trajectory_lengths = [x[0] for x in sorted_results]
    sorted_coordinates = [x[1] for x in sorted_results]
    sorted_mvdr_results = [x[2] for x in sorted_results]
    seq_len = sorted_trajectory_lengths[0]-2
    # seq_len = 10
    max_offsets = [x.shape[0] - seq_len  for x in sorted_coordinates]
    offsets = [np.random.randint(0, offset) for offset in max_offsets]
    cut_coordinates = [x[offsets[i]:offsets[i]+seq_len  , :] \
            for i, x in enumerate(sorted_coordinates)]
    
    cut_mvdr_results = [y[offsets[j]:offsets[j]+seq_len ,:,:] \
            for j, y in enumerate(sorted_mvdr_results)]
    cut_coordinates = torch.stack(cut_coordinates)
    cut_mvdr_results = torch.stack(cut_mvdr_results)
    return cut_coordinates, cut_mvdr_results
    
def find_npyandtxt_files(data_folder, txt_folder):
    txt_files = []
    npy_files = []
    for root, dirs, files in os.walk(data_folder):
        for file in files:
            if file.endswith('.npy') :
                npy_files.append(os.path.join(root, file))
                # 构建原文件和新文件的完整路径
                new_file_name = file[:-4] + '.txt'  # 替换扩展名
                # 确保在目标文件夹中保持子文件夹结构
                relative_path = os.path.relpath(root, data_folder)
                new_subfolder = os.path.join(txt_folder, relative_path)
                new_file_path = os.path.join(new_subfolder, new_file_name)
                txt_files.append(new_file_path)
    return txt_files, npy_files

def find_ptandtxt_files(data_folder, txt_folder):
    txt_files = []
    npy_files = []
    for root, dirs, files in os.walk(data_folder):
        for file in files:
            if file.endswith('.pt') :
                npy_files.append(os.path.join(root, file))
                # 构建原文件和新文件的完整路径
                new_file_name = file[:-3] + '.txt'  # 替换扩展名
                # 确保在目标文件夹中保持子文件夹结构
                relative_path = os.path.relpath(root, data_folder)
                new_subfolder = os.path.join(txt_folder, relative_path)
                new_file_path = os.path.join(new_subfolder, new_file_name)
                txt_files.append(new_file_path)
    return txt_files, npy_files

class RadarRawDataset(Dataset):
    def __init__(self, data_folder, dest_folder=None):
        self.txt_files, self.npy_files = find_npyandtxt_files(data_folder, dest_folder)

    def __len__(self):
        return len(self.txt_files)

    def __getitem__(self, idx):
        return self.txt_files[idx], self.npy_files[idx]
    
class RadarRAMDataset(Dataset):
    def __init__(self, data_folder, dest_folder=None):
        global preprocess
        preprocess = True
        self.txt_files, self.npy_files = find_ptandtxt_files(data_folder, dest_folder)

    def __len__(self):
        return len(self.txt_files)

    def __getitem__(self, idx):
        return self.txt_files[idx], self.npy_files[idx]

if __name__ == '__main__':


    dataset = RadarRawDataset('/media/kb501/diskA/CQ/Tkwer/simmimo24/data_radar_raw_sim', '/media/kb501/diskA/CQ/Tkwer/simmimo24/data_radar_traj_sim')
    data_loader = DataLoader(dataset,
                             batch_size=10,
                             shuffle=True,
                             sampler=None,
                             num_workers=0,
                             collate_fn=collate_func)

    for i, batch in enumerate(data_loader):
        print(i)
        print(batch[0].shape)
        print(batch[1].shape)
