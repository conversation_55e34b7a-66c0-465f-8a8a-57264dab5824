step_decay: 0.1            # 当 lr_schedule_type 为 'step' 时的衰减率  
noam_factor: 1              # 当 lr_schedule_type 为 'noam' 时的因子 (例如)
fix_learning_rate: False  # 固定学习率，如果不使用则设为 null 或 false  
lr_schedule_type: "step"   # 学习率调度类型，可以是 'step' 或 'noam'  
initial_learning_rate: 0.001 # 初始学习率  
step_gamma: 0.98            # 当 lr_schedule_type 为 'step' 时的衰减因子  
lr_step_interval: 100      # 学习率衰减的步数  
noam_warm_up_steps: 400    # 当 lr_schedule_type 为 'noam' 时的预热步数 
save_every_step: 200
evaluate_every_step: 10000
jerk_loss_weight: 0.8 

feature_dims: 64   
bits: 8 
nepochs: 200
grad_norm: 10

adam_params:  
  initial_learning_rate: 0.001  
  adam_beta1: 0.9  
  adam_beta2: 0.999  
  adam_eps: 0.00000001  
  weight_decay: 0  
  amsgrad: false
  
# hybrid CTC/attention
model_conf:
    track_dims: 2
    hidden_size: 256
    bits: 8
    num_layers: 2
    training: false
    use_fc: true
    rnn_type: 'LSTM'
    resnet_layer_configs:
        - [1, 4]
        - [4, 4]
        - [4, 1]
