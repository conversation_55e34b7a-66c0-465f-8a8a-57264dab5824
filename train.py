import os
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
matplotlib.use('TkAgg')  # 或者 'Qt5Agg'，根据你的环境选择  
from datetime import datetime
from os.path import join
from tensorboardX import SummaryWriter
import torch
from torch import nn
from torch import optim
from torch.utils.data import DataLoader
from tqdm import tqdm
import yaml
import argparse
from dataset import RadarRawDataset, RadarRAMDataset, collate_func
from loss_function import nll_loss, regression_loss, compute_jerk_loss
from lrschedule import noam_learning_rate_decay, step_learning_rate_decay
from models.model_onlymeasure import build_model
from utils.quant import quantize, dequantize

global_step = 0
global_epoch = 0
global_test_step = 0
use_cuda = torch.cuda.is_available()


def save_checkpoint(device, model, optimizer, step, checkpoint_dir, epoch):
    checkpoint_path = join(
        checkpoint_dir, "checkpoint_step{:09d}.pth".format(step))
    optimizer_state = optimizer.state_dict()
    global global_test_step
    torch.save({
        "state_dict": model.state_dict(),
        "optimizer": optimizer_state,
        "global_step": step,
        "global_epoch": epoch,
        "global_test_step": global_test_step,
    }, checkpoint_path)
    print("Saved checkpoint:", checkpoint_path)


def _load(checkpoint_path):
    if use_cuda:
        checkpoint = torch.load(checkpoint_path)
    else:
        checkpoint = torch.load(checkpoint_path,
                                map_location=lambda storage, loc: storage)
    return checkpoint


def load_checkpoint(path, model, optimizer, reset_optimizer):
    global global_step
    global global_epoch
    global global_test_step

    print("Load checkpoint from: {}".format(path))
    checkpoint = _load(path)
    model.load_state_dict(checkpoint["state_dict"], strict=False)
    if not reset_optimizer:
        optimizer_state = checkpoint["optimizer"]
        if optimizer_state is not None:
            print("Load optimizer state from {}".format(path))
            try:
                optimizer.load_state_dict(checkpoint["optimizer"])
            except Exception as e:
                print(e)
    global_step = checkpoint["global_step"]
    global_epoch = checkpoint["global_epoch"]
    global_test_step = checkpoint.get("global_test_step", 0)

    return model


def evaluate_model(model, data_loader):
    """evaluate model and save generated wav and plot

    """
    model.eval()  
    with torch.no_grad(): 
        for i, (track, feature) in enumerate(tqdm(data_loader)):
           
           # 使用Teacher Forcing  
            track = track.to(device)
            feature = feature.unsqueeze(2).to(device)
            # outputs = model.inference(track, feature)
            outputs = model.inference(track, feature)
            # outputs= dequantize(outputs.argmax(dim=-1), bits=configs['model_conf']['bits'])
            # quantized_track = quantize(track[:, 1:, :], configs['model_conf']['bits'])
            # outputs= dequantize(quantized_track, bits=configs['model_conf']['bits'])
            plt.cla()
            # plt.plot(outputs.squeeze().cpu(), label='sin', color='blue')  # 第一条曲线  
            # plt.plot(track.squeeze().cpu(), label='cos', color='orange')  # 第二条曲线   
            plt.plot(track.squeeze().cpu()[:,0], track.squeeze().cpu()[:,1], label='cos', color='blue')  # 第二条曲线 
            plt.plot(outputs.squeeze().cpu()[1:,0], outputs.squeeze().cpu()[1:,1], label='cos', color='orange')  # 第二条曲线 
            plt.pause(2)
            a= 1 


def train_loop(configs, device, model, data_loader, data_loader_test, optimizer, checkpoint_dir):
    """Main training loop.

    """
    # create loss and put on device
    # criterion = nll_loss
    criterion = nn.MSELoss()  # 使用均方误差进行回归

    global global_step, global_epoch, global_test_step
    while global_epoch < configs['nepochs']:
        running_loss = 0
        for i, (track, feature) in enumerate(tqdm(data_loader)):
           
           # 使用Teacher Forcing  
            track = track.to(device)
            feature = feature.unsqueeze(2).to(device)
            log_probs = model(track, feature)
            # 回归损失
            jerk_loss = configs['jerk_loss_weight'] *compute_jerk_loss(log_probs)
            loss1 = criterion(log_probs, track[:, 1:, :])
            loss = loss1 + jerk_loss
# 交叉损失
            # log_probs = log_probs.permute(0, 2, 1)  # (B, class, L, track_dims)
            # # 量化 track 数据
            # quantized_track = quantize(track[:, 1:, :], configs['model_conf']['bits'])

            # # 确保这两个张量都是连续的
            # log_probs = log_probs.contiguous()
            # quantized_track = quantized_track.contiguous()

            # loss1 = criterion(log_probs[:,:256,:], quantized_track[:,:,0])
            # loss2 = criterion(log_probs[:,256:,:], quantized_track[:,:,1])
            # loss = loss1 + loss2
            # calculate learning rate and update learning rate
            # 计算学习率并更新学习率  
        
            if configs['fix_learning_rate']:  
                current_lr = configs['fix_learning_rate'] 
                # print("using fixed learning rate of: {}".format(configs['fix_learning_rate']))  
            elif configs['lr_schedule_type'] == 'step':  
                current_lr = step_learning_rate_decay(configs['initial_learning_rate'], global_step, configs['step_gamma'],  
                                                    configs['lr_step_interval'])
                # print("using exponential learning rate decay")   
            else:  # 假设 lr_schedule_type 是 'noam'  
                current_lr = noam_learning_rate_decay(configs['initial_learning_rate'], global_step, configs['noam_warm_up_steps']) 
                # print("using noam/default learning rate decay")  

            # 更新优化器中的学习率  2
            for param_group in optimizer.param_groups:  
                param_group['lr'] = current_lr  
    
            optimizer.zero_grad()
            loss.backward()
            # clip gradient norm
            grad_norm = nn.utils.clip_grad_norm_(model.parameters(), configs['grad_norm'])
            optimizer.step()

            running_loss += loss.item()
            avg_loss = running_loss / (i + 1)

            writer.add_scalar("loss", float(loss.item()), global_step)
            writer.add_scalar("avg_loss", float(avg_loss), global_step)
            writer.add_scalar("learning_rate", float(current_lr), global_step)
            writer.add_scalar("grad_norm", float(grad_norm), global_step)

            # saving checkpoint if needed
            if global_step != 0 and global_step % configs['save_every_step'] == 0:
                # pruner.prune(global_step)
                save_checkpoint(device, model, optimizer, global_step, checkpoint_dir, global_epoch)
            # evaluate model if needed
            if global_step != 0 and global_test_step != True and global_step % configs['evaluate_every_step'] == 0:
                # pruner.prune(global_step)
                print("step {}, evaluating model: generating wav from mel...".format(global_step))
                # evaluate_model(model, data_loader_test, checkpoint_dir)
                print("evaluation finished, resuming training...")

            # reset global_test_step status after evaluation
            if global_test_step is True:
                global_test_step = False
            global_step += 1

        print("epoch:{}, running loss:{}, average loss:{}, current lr:{}".format(global_epoch, running_loss, avg_loss,
                                                                                 current_lr))
        global_epoch += 1

 
datasetreader = {"RadarRawDataset":RadarRawDataset, "RadarRAMDataset":RadarRAMDataset}
if __name__ == "__main__":

    print("Current Working Directory:", os.getcwd())

    parser = argparse.ArgumentParser(description='training your network')
    parser.add_argument('--config', default='configs/configfile.yaml', help='config file')
    parser.add_argument('--train_data', default='/media/kb501/diskA/CQ/Tkwer/simmimo24/dataset_splits/train_manifest.txt', help='train data file')
    parser.add_argument('--cv_data', default='/media/kb501/diskA/CQ/Tkwer/simmimo24/dataset_splits/validation_manifest.txt', help='cv data file')
    parser.add_argument('--gpu',type=int, default=0, help='gpu id for this rank, -1 for cpu')
    parser.add_argument('--checkpoint_dir', default='checkpoints', help='checkpoint model folder')
    parser.add_argument('--checkpoint', default='checkpoints/checkpoint_step000000851.pth', help='checkpoint model folder')
    parser.add_argument('--tensorboard_dir', default='tensorboard', help='tensorboard log dir')
    parser.add_argument('--num_workers', default=0, type=int, help='num of subprocess workers for reading')
    parser.add_argument('--dataset_type', default="RadarRawDataset" ,  help='dataset type')

    args = parser.parse_args()

    # make dirs, load dataloader and set up device
    os.makedirs(args.checkpoint_dir, exist_ok=True)
    os.makedirs(os.path.join(args.checkpoint_dir, 'eval'), exist_ok=True)
    with open(args.config, 'r', encoding='utf-8') as fin:
        configs = yaml.load(fin,Loader = yaml.FullLoader)

    dataset = datasetreader[args.dataset_type](args.train_data)
    dataset_test = datasetreader[args.dataset_type](args.cv_data)

    
    data_loader = DataLoader(dataset, collate_fn=collate_func, shuffle=True, num_workers=int(args.num_workers),
                             batch_size=24)
    data_loader_test = DataLoader(dataset_test, collate_fn=collate_func, shuffle=True, num_workers=int(args.num_workers),
                            batch_size=1)
    device = torch.device("cuda" if use_cuda else "cpu")
    print("using device:{}".format(device))

    if args.tensorboard_dir is None:
        args.tensorboard_dir = "tensorboard_log/log_" + datetime.now().strftime("%Y%m%d-%H%M%S")
    else:
        args.tensorboard_dir += "/" + datetime.now().strftime("%Y%m%d-%H%M%S")
    print("Tensorboard event path: {}".format(args.tensorboard_dir))
    writer = SummaryWriter(log_dir=args.tensorboard_dir)

    # build model, create optimizer
    model = build_model(configs).to(device)
    print(model)
    
    # 读取 Adam 优化器的参数
    adam_params = configs['adam_params']
    adam_kwargs = {  
        'lr': adam_params['initial_learning_rate'],  
        'betas': (adam_params['adam_beta1'], adam_params['adam_beta2']),  
        'eps': adam_params['adam_eps'],  
        'weight_decay': adam_params['weight_decay'],  
        'amsgrad': adam_params['amsgrad']  
    }  
    optimizer = optim.Adam(model.parameters(),**adam_kwargs)



    # load checkpoint
    if args.checkpoint is None:
        print("no checkpoint specified as --checkpoint argument, creating new model...")
    else:
        model = load_checkpoint(args.checkpoint, model, optimizer, True) #ei False
        print("loading model from checkpoint:{}".format(args.checkpoint))
        # set global_test_step to True so we don't evaluate right when we load in the model
        global_test_step = True
    # evaluate_model(model, data_loader_test)
    # main train loop
    try:
        train_loop(configs, device, model, data_loader, data_loader_test, optimizer, args.checkpoint_dir)
    except KeyboardInterrupt:
        print("Interrupted!")
        pass
    except Exception as e:
        print(e)
    finally:
        print("saving model....")
        save_checkpoint(device, model, optimizer, global_step, args.checkpoint_dir, global_epoch)

