import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import numpy as np 
from dataset import read_and_process_txt

# Step 1: 自定义数据集类
class SpotDataset(Dataset):
    def __init__(self, images, coords, transform=None):
        self.images = images  # 灰度图像 (batch, 1, H, W)
        self.coords = coords  # 光斑中心坐标 (batch, 2)
        self.transform = transform

    def __len__(self):
        return len(self.images)

    def __getitem__(self, idx):
        image = self.images[idx]
        coord = self.coords[idx]
        if self.transform:
            image = self.transform(image)
        return image, coord

# Step 2: 定义简单的CNN模型
class SpotDetector(nn.Module):
    def __init__(self):
        super(SpotDetector, self).__init__()
        self.conv1 = nn.Conv2d(1, 16, kernel_size=3, stride=1, padding=1)
        self.conv2 = nn.Conv2d(16, 32, kernel_size=3, stride=1, padding=1)
        self.conv3 = nn.Conv2d(32, 64, kernel_size=3, stride=1, padding=1)
        self.fc1 = nn.Linear(64*64*64, 128)
        self.fc2 = nn.Linear(128, 2)  # 输出2个值 (x, y)

    def forward(self, x):
        x = torch.relu(self.conv1(x))
        x = torch.relu(self.conv2(x))
        x = torch.relu(self.conv3(x))
        # x = F.adaptive_avg_pool2d(x, (1, 1))
        # x = x.squeeze()    
        x = torch.flatten(x, 1)
        x = torch.relu(self.fc1(x))
        x = self.fc2(x)
        return x  # 返回中心坐标 (x, y)

# Step 3: 使用 numpy 生成光斑
def generate_spot_image(image_size, center, radius=5):
    """生成一个包含光斑的图像，光斑是高斯函数形式."""
    x = np.arange(0, image_size, 1, float)
    y = x[:, np.newaxis]
    x0, y0 = center
    # 高斯分布模拟光斑
    spot = np.exp(-4 * np.log(2) * ((x - x0)**2 + (y - y0)**2) / radius**2)
        # 遍历 Batch 中的每一个图像
    min_val =  np.min(spot)
    max_val =  np.max(spot)
    
    # 防止除以零，避免 max_val 和 min_val 相等时产生 NaN
    normalized_spot = (spot - min_val) / (max_val - min_val + 1e-8)
    return normalized_spot

# Step 4: 训练模型的函数
def train_model(model, dataloader, optimizer, num_epochs=10):
    criterion = nn.MSELoss()  # 使用均方误差进行回归
    model.train()
    for epoch in range(num_epochs):
        running_loss = 0.0
        for images, coords in dataloader:
            images = torch.tensor(images, dtype=torch.float32).unsqueeze(1)  # 添加通道维度 (batch, 1, H, W)
            coords = torch.tensor(coords, dtype=torch.float32)
            
            optimizer.zero_grad()
            outputs = model(images)
            
            loss = criterion(outputs, coords)
            loss.backward()
            optimizer.step()
            
            running_loss += loss.item()
        
        print(f'Epoch [{epoch+1}/{num_epochs}], Loss: {running_loss/len(dataloader)}')

# Step 1: 定义加载模型的函数
def load_model(model_path):
    # 实例化模型结构
    model = SpotDetector()
    # 加载模型权重
    model.load_state_dict(torch.load(model_path))
    model.eval()  # 设置模型为评估模式
    return model

# Step 2: 定义预测函数
def predict_spot_center(model, image):
    """
    使用加载的模型对输入图像进行预测，返回光斑中心的 (x, y) 坐标
    :param model: 训练好的模型
    :param image: 输入的单张灰度图像 (H, W)
    :return: 光斑中心的 (x, y) 坐标
    """
    # 将图像转换为适合模型的输入格式 (1, 1, H, W)
    image = torch.tensor(image, dtype=torch.float32).unsqueeze(0).unsqueeze(0)  # (1, 1, H, W)

    with torch.no_grad():  # 禁用梯度计算，减少内存消耗
        output = model(image)  # 预测光斑的中心坐标

    return output.squeeze().numpy()  # 返回预测的坐标 (x, y)

# Step 4: 构建数据集和训练流程
if __name__ == "__main__":
    # 假设生成了一些随机的光斑数据 (100 张图片，每张 64x64)
    image_size = 64
    num_samples = 100
    images = []
    coords = []
    import matplotlib.pyplot as plt

    for _ in range(num_samples):
        x = np.random.randint(10, image_size - 10)
        y = np.random.randint(10, image_size - 10)
        img = generate_spot_image(image_size, (x, y))
        images.append(img)
        # 使用matplotlib显示生成的光斑图像
        plt.imshow(img, cmap='gray')
        plt.title(f"Spot at center: {(x, y)}")
        # plt.colorbar()  # 添加颜色条用于显示亮度值
        plt.pause(0.01)
        coords.append([x, y])  # 存储光斑中心
        
    # trajectory_length, coords  = read_and_process_txt('G:/IAHEW-UCAS2016-raw/data/01/pump_201628007329018.txt')
    # images = torch.load('G:/IAHEW-UCAS2016-raw/data_radar_raw_sim_preprocess/01/pump_201628007329018.pt', weights_only=True, map_location='cpu')
    # coords = coords.cpu()
    # images = np.array(images)
    # coords = np.array(coords)
    
    # 创建数据集和数据加载器
    dataset = SpotDataset(images, coords)
    dataloader = DataLoader(dataset, batch_size=4, shuffle=True)
    
    # 实例化模型、优化器
    model = SpotDetector()
    optimizer = optim.Adam(model.parameters(), lr=0.001)
    
    # 训练模型
    train_model(model, dataloader, optimizer, num_epochs=16)
    torch.save(model.state_dict(), 'spot_detector_model.pth')

    # 加载训练好的模型
    model_path = 'spot_detector_model.pth'
    model = load_model(model_path)
    
    # 生成一张新的光斑图像进行预测
    image_size = 64
    # trajectory_length, coords  = read_and_process_txt('G:/IAHEW-UCAS2016-raw/data/01/mail_201618013229045.txt')
    # images = torch.load('G:/IAHEW-UCAS2016-raw/data_radar_raw_sim_preprocess/01/mail_201618013229045.pt', weights_only=True, map_location='cpu')
    for i in range(135):
        x_true, y_true = np.random.randint(10, image_size - 10, size=2)
        test_image = generate_spot_image(image_size, (x_true, y_true))  # 生成光斑图像
        
        # 使用模型预测光斑中心坐标
        x_true, y_true = coords[i]
        predicted_coords = predict_spot_center(model, images[i])
        print(f"真实坐标: ({x_true}, {y_true}), 预测坐标: {predicted_coords}")