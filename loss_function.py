import torch
from torch.nn import functional as F
import numpy as np


def nll_loss(y_hat, y, reduce=True):
    loss = F.nll_loss(y_hat, y)
    return loss

# 自定义均方误差损失函数
def regression_loss(predictions, targets):
    l1_loss = F.l1_loss(predictions, targets)
    return F.mse_loss(predictions, targets) +0.1*l1_loss # 使用 PyTorch 内置的 MSE 损失

def compute_jerk_loss(outputs):
    """
    计算输出轨迹的最小 jerk 损失。
    
    参数:
        outputs: 模型输出的轨迹, 形状为 [batch_size, seq_len, track_dims]
    
    返回:
        jerk_loss: 最小 jerk 损失值
    """
    # Step 1: 一阶差分 (velocity)
    velocities = outputs[:, 1:, :] - outputs[:, :-1, :]

    # Step 2: 二阶差分 (acceleration)
    accelerations = velocities[:, 1:, :] - velocities[:, :-1, :]

    # Step 3: 三阶差分 (jerk)
    jerk = accelerations[:, 1:, :] - accelerations[:, :-1, :]

    # Step 4: 计算 jerk 的平方和 (L2范数)
    jerk_loss = torch.mean(jerk ** 2)

    return jerk_loss

def dtw_loss(x, y, gamma=1.0):
    """
    Dynamic Time Warping (DTW) 损失函数

    Args:
        x: 预测序列 (batch_size, seq_len_x, feature_dim) - 可以是任意长度
        y: 目标序列 (batch_size, seq_len_y, feature_dim) - 可以是任意长度
        gamma: 软DTW的平滑参数，gamma越小越接近硬DTW

    Returns:
        DTW损失值
    """
    batch_size, seq_len_x, feature_dim = x.shape
    _, seq_len_y, _ = y.shape

    # 计算距离矩阵 - 支持不同长度的序列
    # x: (batch_size, seq_len_x, 1, feature_dim)
    # y: (batch_size, 1, seq_len_y, feature_dim)
    x_expanded = x.unsqueeze(2)  # (batch_size, seq_len_x, 1, feature_dim)
    y_expanded = y.unsqueeze(1)  # (batch_size, 1, seq_len_y, feature_dim)

    # 计算欧几里得距离矩阵
    dist_matrix = torch.sum((x_expanded - y_expanded) ** 2, dim=-1)  # (batch_size, seq_len_x, seq_len_y)

    # 软DTW算法 - 现在可以处理不同长度的序列
    dtw_distances = []
    for b in range(batch_size):
        dtw_dist = soft_dtw_single(dist_matrix[b], gamma)
        dtw_distances.append(dtw_dist)

    # 返回批次的平均DTW距离
    return torch.stack(dtw_distances).mean()


def soft_dtw_single(dist_matrix, gamma=1.0):
    """
    单个样本的软DTW计算

    Args:
        dist_matrix: 单个样本的距离矩阵 (seq_len_x, seq_len_y)
        gamma: 平滑参数

    Returns:
        DTW距离值
    """
    seq_len_x, seq_len_y = dist_matrix.shape
    device = dist_matrix.device

    # 初始化DTW矩阵
    dtw_matrix = torch.full((seq_len_x + 1, seq_len_y + 1),
                           float('inf'), device=device)
    dtw_matrix[0, 0] = 0

    # 动态规划计算DTW
    for i in range(1, seq_len_x + 1):
        for j in range(1, seq_len_y + 1):
            # 三个可能的路径：从左、从上、从对角线
            candidates = torch.tensor([
                dtw_matrix[i-1, j],      # 从上
                dtw_matrix[i, j-1],      # 从左
                dtw_matrix[i-1, j-1]     # 从对角线
            ], device=device)

            # 软最小值（使用logsumexp技巧）
            if gamma > 0:
                soft_min = -gamma * torch.logsumexp(-candidates / gamma, dim=0)
            else:
                soft_min = torch.min(candidates)

            dtw_matrix[i, j] = dist_matrix[i-1, j-1] + soft_min

    return dtw_matrix[seq_len_x, seq_len_y]




def dtw_loss_simple(predictions, targets):
    """
    简化版DTW损失函数，适用于相同长度的序列

    Args:
        predictions: 预测序列 (batch_size, seq_len, feature_dim)
        targets: 目标序列 (batch_size, seq_len, feature_dim)

    Returns:
        DTW损失值
    """
    return dtw_loss(predictions, targets, gamma=1.0)


def test_dtw_loss():
    """测试DTW损失函数"""
    print("=== 测试DTW损失函数 ===")

    # 测试1: 相同长度的序列
    print("\n1. 测试相同长度的序列:")
    batch_size, seq_len, feature_dim = 4, 10, 3
    predictions = torch.randn(batch_size, seq_len, feature_dim)
    targets = torch.randn(batch_size, seq_len+3, feature_dim)

    dtw_loss_value = dtw_loss_simple(predictions, targets)
    print(f"   DTW Loss (相同长度): {dtw_loss_value.item():.4f}")

    # 测试相同序列的DTW损失（应该接近0）
    same_seq_loss = dtw_loss_simple(predictions, predictions)
    print(f"   DTW Loss (相同序列): {same_seq_loss.item():.4f}")

    # 测试2: 不同长度的序列
    print("\n2. 测试不同长度的序列:")
    seq_len_x, seq_len_y = 8, 12
    predictions_diff = torch.randn(batch_size, seq_len_x, feature_dim)
    targets_diff = torch.randn(batch_size, seq_len_y, feature_dim)

    dtw_loss_diff = dtw_loss(predictions_diff, targets_diff, gamma=1.0)
    print(f"   DTW Loss (8 vs 12): {dtw_loss_diff.item():.4f}")

    # 测试3: 更大的长度差异
    print("\n3. 测试更大的长度差异:")
    seq_len_x, seq_len_y = 5, 15
    predictions_big_diff = torch.randn(batch_size, seq_len_x, feature_dim)
    targets_big_diff = torch.randn(batch_size, seq_len_y, feature_dim)

    dtw_loss_big_diff = dtw_loss(predictions_big_diff, targets_big_diff, gamma=1.0)
    print(f"   DTW Loss (5 vs 15): {dtw_loss_big_diff.item():.4f}")

    # 测试4: 不同的gamma值
    print("\n4. 测试不同的gamma值:")
    for gamma_val in [0.1, 1.0, 10.0]:
        dtw_loss_gamma = dtw_loss(predictions_diff, targets_diff, gamma=gamma_val)
        print(f"   DTW Loss (gamma={gamma_val}): {dtw_loss_gamma.item():.4f}")

    return dtw_loss_value


if __name__ == "__main__":
    # test_loss()  # 原测试函数有问题，暂时注释掉
    test_dtw_loss()