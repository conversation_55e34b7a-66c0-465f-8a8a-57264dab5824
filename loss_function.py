import torch
from torch.nn import functional as F
import numpy as np


def nll_loss(y_hat, y, reduce=True):
    loss = F.nll_loss(y_hat, y)
    return loss

# 自定义均方误差损失函数
def regression_loss(predictions, targets):
    l1_loss = F.l1_loss(predictions, targets)
    return F.mse_loss(predictions, targets) +0.1*l1_loss # 使用 PyTorch 内置的 MSE 损失


def dtw_loss(x, y, gamma=1.0):
    """
    Dynamic Time Warping (DTW) 损失函数

    Args:
        x: 预测序列 (batch_size, seq_len, feature_dim)
        y: 目标序列 (batch_size, seq_len, feature_dim)
        gamma: 软DTW的平滑参数，gamma越小越接近硬DTW

    Returns:
        DTW损失值
    """
    batch_size, seq_len_x, feature_dim = x.shape
    _, seq_len_y, _ = y.shape

    # 计算距离矩阵
    # x: (batch_size, seq_len_x, 1, feature_dim)
    # y: (batch_size, 1, seq_len_y, feature_dim)
    x_expanded = x.unsqueeze(2)
    y_expanded = y.unsqueeze(1)

    # 计算欧几里得距离
    dist_matrix = torch.sum((x_expanded - y_expanded) ** 2, dim=-1)  # (batch_size, seq_len_x, seq_len_y)

    # 软DTW算法
    dtw_matrix = soft_dtw_forward(dist_matrix, gamma)

    # 返回DTW距离
    return dtw_matrix[:, -1, -1].mean()


def soft_dtw_forward(dist_matrix, gamma=1.0):
    """
    软DTW前向传播

    Args:
        dist_matrix: 距离矩阵 (batch_size, seq_len_x, seq_len_y)
        gamma: 平滑参数

    Returns:
        DTW矩阵
    """
    batch_size, seq_len_x, seq_len_y = dist_matrix.shape
    device = dist_matrix.device

    # 初始化DTW矩阵
    dtw_matrix = torch.full((batch_size, seq_len_x + 1, seq_len_y + 1),
                           float('inf'), device=device)
    dtw_matrix[:, 0, 0] = 0

    # 动态规划计算DTW
    for i in range(1, seq_len_x + 1):
        for j in range(1, seq_len_y + 1):
            # 三个可能的路径：从左、从上、从对角线
            candidates = torch.stack([
                dtw_matrix[:, i-1, j],      # 从上
                dtw_matrix[:, i, j-1],      # 从左
                dtw_matrix[:, i-1, j-1]     # 从对角线
            ], dim=1)

            # 软最小值（使用logsumexp技巧）
            if gamma > 0:
                soft_min = -gamma * torch.logsumexp(-candidates / gamma, dim=1)
            else:
                soft_min = torch.min(candidates, dim=1)[0]

            dtw_matrix[:, i, j] = dist_matrix[:, i-1, j-1] + soft_min

    return dtw_matrix[:, 1:, 1:]


def dtw_loss_simple(predictions, targets):
    """
    简化版DTW损失函数，适用于相同长度的序列

    Args:
        predictions: 预测序列 (batch_size, seq_len, feature_dim)
        targets: 目标序列 (batch_size, seq_len, feature_dim)

    Returns:
        DTW损失值
    """
    return dtw_loss(predictions, targets, gamma=1.0)

def test_loss():
    yhat = torch.rand(16, 100, 54)
    y = torch.rand(16, 100, 1)
    loss = nll_loss(yhat, y.squeeze(-1))