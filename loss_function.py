import torch
from torch.nn import functional as F


def nll_loss(y_hat, y, reduce=True):
    loss = F.nll_loss(y_hat, y)
    return loss

# 自定义均方误差损失函数
def regression_loss(predictions, targets):
    l1_loss = F.l1_loss(predictions, targets)
    return F.mse_loss(predictions, targets) +0.1*l1_loss # 使用 PyTorch 内置的 MSE 损失

def test_loss():
    yhat = torch.rand(16, 100, 54)
    y = torch.rand(16, 100, 1)
    loss = nll_loss(yhat, y.squeeze(-1))